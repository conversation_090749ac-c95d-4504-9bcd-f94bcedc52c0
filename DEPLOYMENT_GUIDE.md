# AnxinlaDB API 服务器部署指南

## 📋 部署前准备

### 系统要求
- **操作系统**: Linux (Ubuntu 18.04+ / CentOS 7+ / Debian 9+)
- **Node.js**: 18.0+ 或 20.0+
- **MySQL**: 5.7+ 或 8.0+
- **内存**: 最低 1GB，推荐 2GB+
- **存储**: 最低 10GB 可用空间

### 必需软件
- Git
- Node.js & npm
- MySQL Server
- PM2 (进程管理器)

## 🚀 快速部署步骤

### 1. 克隆项目到服务器

```bash
# 克隆项目
git clone https://github.com/GYSASR/anxinladb-api.git

# 进入项目目录
cd anxinladb-api

# 查看项目结构
ls -la
```

### 2. 安装依赖

```bash
# 安装项目依赖
npm install

# 全局安装PM2
npm install -g pm2
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

**重要配置项**:
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=anxinlaDB

# 服务器配置
PORT=8080
NODE_ENV=production

# 安全配置
ALLOWED_ORIGINS=http://your-frontend-domain.com
RATE_LIMIT_WINDOW_MS=300000
RATE_LIMIT_MAX_REQUESTS=1000
```

### 4. 数据库设置

```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE anxinlaDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户并授权
CREATE USER 'anxinla_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON anxinlaDB.* TO 'anxinla_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 5. 启动服务

```bash
# 使用PM2启动服务
pm2 start ecosystem.config.js

# 查看服务状态
pm2 status

# 查看日志
pm2 logs anxinladb-api

# 设置开机自启
pm2 startup
pm2 save
```

## 🔧 管理命令

### PM2 管理脚本
```bash
# 使用项目提供的管理脚本
./pm2.sh start     # 启动服务
./pm2.sh stop      # 停止服务
./pm2.sh restart   # 重启服务
./pm2.sh status    # 查看状态
./pm2.sh logs      # 查看日志
./pm2.sh monitor   # 监控服务
```

### NPM 脚本
```bash
npm run pm2:start    # 启动服务
npm run pm2:stop     # 停止服务
npm run pm2:restart  # 重启服务
npm run pm2:status   # 查看状态
npm run pm2:logs     # 查看日志
```

### Git 更新部署
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖（如有）
npm install

# 重启服务
pm2 restart anxinladb-api

# 或使用脚本
./pm2.sh restart
```

## 🔍 验证部署

### 1. 健康检查
```bash
# 检查服务状态
curl http://localhost:8080/health

# 预期响应
{
  "success": true,
  "message": "API服务器运行正常",
  "data": {
    "status": "healthy",
    "database": "connected",
    "timestamp": "2025-07-22T16:00:00.000Z"
  }
}
```

### 2. API 测试
```bash
# 测试统计接口
curl http://localhost:8080/api/v1/statistics/totals

# 测试用户接口
curl http://localhost:8080/api/v1/users/all
```

## 🛡️ 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 8080
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

### 2. Nginx 反向代理（推荐）
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📊 监控和日志

### 日志位置
- **应用日志**: `logs/combined.log`
- **错误日志**: `logs/error.log`
- **输出日志**: `logs/out.log`
- **PM2日志**: `~/.pm2/logs/`

### 监控命令
```bash
# 实时监控
pm2 monit

# 查看资源使用
pm2 show anxinladb-api

# 重启计数
pm2 restart anxinladb-api --watch
```

## 🔄 自动化部署脚本

创建 `deploy.sh`:
```bash
#!/bin/bash
echo "🚀 开始部署 AnxinlaDB API..."

# 拉取最新代码
git pull origin main

# 安装依赖
npm install --production

# 重启服务
pm2 restart anxinladb-api

# 验证部署
sleep 5
curl -f http://localhost:8080/health || exit 1

echo "✅ 部署完成！"
```

## 📞 故障排除

### 常见问题
1. **端口被占用**: 修改 `.env` 中的 `PORT` 配置
2. **数据库连接失败**: 检查数据库配置和权限
3. **PM2 启动失败**: 检查 Node.js 版本和权限
4. **内存不足**: 增加服务器内存或优化配置

### 日志查看
```bash
# 查看最近的错误日志
tail -f logs/error.log

# 查看PM2日志
pm2 logs anxinladb-api --lines 100
```

## 📚 相关文档
- [API接口文档](./API_DOCUMENTATION.md)
- [PM2官方文档](https://pm2.keymetrics.io/)
- [Node.js部署指南](https://nodejs.org/en/docs/guides/)
