#!/bin/bash

# AnxinlaDB API PM2 管理脚本

case "$1" in
  start)
    echo "启动 AnxinlaDB API 服务..."
    pm2 start ecosystem.config.js
    ;;
  stop)
    echo "停止 AnxinlaDB API 服务..."
    pm2 stop anxinladb-api
    ;;
  restart)
    echo "重启 AnxinlaDB API 服务..."
    pm2 restart anxinladb-api
    ;;
  reload)
    echo "重载 AnxinlaDB API 服务..."
    pm2 reload anxinladb-api
    ;;
  status)
    echo "查看 AnxinlaDB API 服务状态..."
    pm2 status
    ;;
  logs)
    echo "查看 AnxinlaDB API 服务日志..."
    pm2 logs anxinladb-api
    ;;
  monitor)
    echo "监控 AnxinlaDB API 服务..."
    pm2 monit
    ;;
  delete)
    echo "删除 AnxinlaDB API 服务..."
    pm2 delete anxinladb-api
    ;;
  *)
    echo "用法: $0 {start|stop|restart|reload|status|logs|monitor|delete}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  reload  - 重载服务（零停机时间）"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看服务日志"
    echo "  monitor - 监控服务"
    echo "  delete  - 删除服务"
    exit 1
    ;;
esac
