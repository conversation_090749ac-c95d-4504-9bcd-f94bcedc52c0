{"name": "anxinladb-api", "version": "1.0.0", "description": "AnxinlaDB Backend API Server", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop anxinladb-api", "pm2:restart": "pm2 restart anxinladb-api", "pm2:reload": "pm2 reload anxinladb-api", "pm2:status": "pm2 status", "pm2:logs": "pm2 logs anxinladb-api", "pm2:monitor": "pm2 monit"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "joi": "^17.9.2", "mysql2": "^3.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["api", "mysql", "express", "device-management"], "author": "AnxinlaDB Team", "license": "MIT"}