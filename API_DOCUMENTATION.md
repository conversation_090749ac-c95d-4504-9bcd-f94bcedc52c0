# AnxinlaDB API 接口文档

## 基本信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **速率限制**: 每个IP 5分钟内最多1000个请求

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功描述",
  "data": {}, // 具体数据
  "timestamp": "2025-07-22T16:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "错误代码",
    "message": "错误描述",
    "details": "详细错误信息"
  },
  "timestamp": "2025-07-22T16:00:00.000Z"
}
```

### 分页响应格式
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "items": [], // 数据列表
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "pages": 10
    }
  },
  "timestamp": "2025-07-22T16:00:00.000Z"
}
```

## 1. 用户管理 (Users)

### 1.1 获取用户列表
- **URL**: `GET /users`
- **参数**: 
  - `page` (可选): 页码，默认1
  - `limit` (可选): 每页数量，默认10
  - `user_name` (可选): 用户名搜索
- **响应**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "user_id": 1,
        "name": "台州",
        "department": null,
        "contact": null,
        "created_at": "2025-07-22T06:50:35.000Z",
        "updated_at": "2025-07-22T14:21:31.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 4,
      "pages": 1
    }
  },
  "timestamp": "2025-07-22T16:08:07.569Z"
}
```

### 1.2 获取所有用户（下拉选择）
- **URL**: `GET /users/all`
- **响应**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": [
    {
      "user_id": 1,
      "user_name": "台州"
    },
    {
      "user_id": 2,
      "user_name": "临海"
    }
  ],
  "timestamp": "2025-07-22T16:07:58.109Z"
}
```

### 1.3 创建用户
- **URL**: `POST /users`
- **请求体**:
```json
{
  "name": "用户名",
  "department": "部门",
  "contact": "联系方式"
}
```
- **响应**: 返回创建的用户信息

### 1.4 获取用户详情
- **URL**: `GET /users/{id}`
- **响应**: 返回单个用户详细信息

### 1.5 更新用户
- **URL**: `PUT /users/{id}`
- **请求体**:
```json
{
  "user_name": "新用户名"
}
```

### 1.6 删除用户
- **URL**: `DELETE /users/{id}`
- **响应**: 
```json
{
  "success": true,
  "message": "删除成功",
  "data": null,
  "timestamp": "2025-07-22T16:00:00.000Z"
}
```

## 2. 设备类型管理 (Device Types)

### 2.1 获取设备类型列表
- **URL**: `GET /device-types`
- **参数**: 
  - `page`, `limit`: 分页参数
  - `type_name`: 类型名称搜索
- **响应**: 分页格式的设备类型列表

### 2.2 获取所有设备类型
- **URL**: `GET /device-types/all`
- **响应**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": [
    {
      "type_id": 1008,
      "type_name": "平板车500"
    },
    {
      "type_id": 1009,
      "type_name": "箱货"
    },
    {
      "type_id": 1010,
      "type_name": "面包车200"
    }
  ],
  "timestamp": "2025-07-22T16:09:29.398Z"
}
```

### 2.3 创建设备类型
- **URL**: `POST /device-types`
- **请求体**:
```json
{
  "type_id": 1015,
  "type_name": "新设备类型"
}
```

### 2.4 其他操作
- `GET /device-types/{id}`: 获取详情
- `PUT /device-types/{id}`: 更新
- `DELETE /device-types/{id}`: 删除

## 3. 存储位置管理 (Storage Locations)

### 3.1 获取存储位置列表
- **URL**: `GET /storage-locations`
- **参数**: 
  - `page`, `limit`: 分页参数
  - `min_capacity`, `max_capacity`: 容量范围筛选

### 3.2 获取所有存储位置
- **URL**: `GET /storage-locations/all`
- **响应**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": [
    {
      "location_id": 1,
      "capacity": 150,
      "current_usage": 11,
      "available_capacity": 139
    }
  ],
  "timestamp": "2025-07-22T16:00:00.000Z"
}
```

### 3.3 创建存储位置
- **URL**: `POST /storage-locations`
- **请求体**:
```json
{
  "location_id": 2003,
  "capacity": 50
}
```

## 4. 设备管理 (Devices)

### 4.1 获取设备列表
- **URL**: `GET /devices`
- **参数**:
  - `page`, `limit`: 分页参数
  - `device_type`: 设备类型筛选
  - `storage_location`: 存储位置筛选
  - `status`: 状态筛选 (available/outbound)
  - `search`: 设备ID搜索
- **响应**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "device_id": "B01231101490",
        "device_type": {
          "type_id": 1009,
          "type_name": "箱货"
        },
        "storage_location": {
          "location_id": 20,
          "capacity": 10,
          "available_capacity": 0
        },
        "description": "",
        "status": "available",
        "created_at": "2025-07-18T09:42:38.000Z",
        "updated_at": "2025-07-22T15:02:49.000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 116,
      "total_items": 232,
      "items_per_page": 2
    }
  },
  "timestamp": "2025-07-22T16:09:19.030Z"
}
```

### 4.2 创建设备
- **URL**: `POST /devices`
- **请求体**:
```json
{
  "device_id": "DEV001",
  "device_type": 1014,
  "storage_location": 1,
  "description": "设备描述"
}
```
- **响应**: 返回创建的设备详细信息（包含关联的设备类型和存储位置信息）

### 4.3 其他操作
- `GET /devices/{device_id}`: 获取设备详情
- `PUT /devices/{device_id}`: 更新设备
- `DELETE /devices/{device_id}`: 删除设备（如有出库记录会返回409错误）

## 5. 出库管理 (Outbound)

### 5.1 获取出库记录列表
- **URL**: `GET /outbound`
- **参数**: 分页参数
- **响应**: 包含设备信息和用户信息的出库记录列表

### 5.2 创建出库记录
- **URL**: `POST /outbound`
- **请求体**:
```json
{
  "device_id": "DEV001",
  "user_id": 1,
  "purpose": "出库用途",
  "expected_return_date": "2025-08-01"
}
```

### 5.3 设备归还
- **URL**: `PUT /outbound/{id}/return`

### 5.4 取消出库
- **URL**: `DELETE /outbound/{id}`

## 6. 统计信息 (Statistics)

### 6.1 总数统计
- **URL**: `GET /statistics/totals`
- **响应**:
```json
{
  "success": true,
  "data": {
    "devices": 232,
    "device_types": 7,
    "storage_locations": 21,
    "outbound_records": 0,
    "users": 4,
    "total": 264
  },
  "message": "总数统计获取成功",
  "timestamp": "2025-07-22T15:44:03.865Z"
}
```

### 6.2 综合统计
- **URL**: `GET /statistics/overview`
- **响应**: 详细的设备、存储、出库统计信息

### 6.3 其他统计
- `GET /statistics/devices`: 设备统计
- `GET /statistics/storage-capacity`: 存储容量统计
- `GET /statistics/outbound`: 出库统计

## 7. 系统信息

### 7.1 健康检查
- **URL**: `GET /health`
- **响应**: 系统健康状态和基本信息

### 7.2 API信息
- **URL**: `GET /api/v1`
- **响应**: API版本和端点信息

## 错误代码说明

- `VAL_001`: 参数验证失败
- `BIZ_001`: 资源不存在
- `BIZ_004`: 删除约束违反
- `SYS_002`: 内部服务器错误
- `RATE_LIMIT`: 请求过于频繁

## 前端对接注意事项

### 1. 响应格式统一
所有接口都遵循统一的响应格式，前端可以通过 `success` 字段判断请求是否成功。

### 2. 分页处理
- 列表接口支持分页，注意 `pagination` 对象中的字段名可能不同
- 设备列表使用: `current_page`, `total_pages`, `total_items`, `items_per_page`
- 其他列表使用: `page`, `limit`, `total`, `pages`

### 3. 关联数据
设备列表会自动包含设备类型和存储位置的详细信息，无需额外请求。

### 4. 状态管理
- 设备状态: `available` (可用), `outbound` (已出库)
- 删除操作可能因为关联数据而失败，需要处理409错误

### 5. 下拉选择数据
各模块都提供 `/all` 端点用于获取下拉选择数据，格式简化便于前端使用。

### 6. 时间格式
所有时间字段都使用ISO 8601格式: `2025-07-22T16:00:00.000Z`

## 常用接口组合

### 设备管理页面
1. `GET /device-types/all` - 获取设备类型选项
2. `GET /storage-locations/all` - 获取存储位置选项
3. `GET /devices` - 获取设备列表

### 出库管理页面
1. `GET /devices?status=available` - 获取可用设备
2. `GET /users/all` - 获取用户选项
3. `GET /outbound` - 获取出库记录

### 统计页面
1. `GET /statistics/totals` - 获取总数统计
2. `GET /statistics/overview` - 获取详细统计
