# AnxinlaDB API

一个简单的设备管理后端API服务。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动服务
```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

### 访问服务
- API服务: http://localhost:8080
- 健康检查: http://localhost:8080/health
- API文档: http://localhost:8080/api/v1

## 📁 项目结构

```
src/
├── app.js              # 主应用入口
├── config/             # 配置文件
├── controllers/        # 控制器
├── middleware/         # 中间件
├── models/             # 数据模型
├── routes/             # 路由定义
└── utils/              # 工具函数
```

## 🔧 环境配置

创建 `.env` 文件并配置以下变量：

```env
# 数据库配置
DB_HOST=localhost
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=anxinlaDB

# 服务配置
PORT=8080
NODE_ENV=development
```

## 📚 API 接口

### 核心接口
- `GET /health` - 健康检查
- `GET /api/v1` - API信息
- `GET /api/v1/devices` - 设备列表
- `GET /api/v1/device-types` - 设备类型
- `GET /api/v1/storage-locations` - 存储位置
- `GET /api/v1/statistics` - 统计信息

详细API文档请查看 `API_DOCUMENTATION.md`

## 🛠️ 开发

```bash
# 安装依赖
npm install

# 开发模式启动
npm run dev

# 生产模式启动
npm start
```

## 📝 许可证

MIT License
