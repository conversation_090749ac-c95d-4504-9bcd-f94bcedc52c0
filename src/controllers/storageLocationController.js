const StorageLocation = require('../models/StorageLocation');

class StorageLocationController {
  // 获取存储位置列表
  static async getList(req, res, next) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const minCapacity = req.query.min_capacity ? parseInt(req.query.min_capacity) : null;
      const maxCapacity = req.query.max_capacity ? parseInt(req.query.max_capacity) : null;

      const result = await StorageLocation.getList(page, limit, minCapacity, maxCapacity);

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 创建存储位置
  static async create(req, res, next) {
    try {
      const { location_id, capacity } = req.body;

      const storageLocation = await StorageLocation.create(capacity, location_id);

      res.status(201).json({
        success: true,
        message: '创建成功',
        data: storageLocation,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取存储位置详情
  static async getById(req, res, next) {
    try {
      const locationId = parseInt(req.params.id);

      const storageLocation = await StorageLocation.findById(locationId);
      if (!storageLocation) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '存储位置不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      // 获取关联的设备
      const devices = await StorageLocation.getDevices(locationId);
      storageLocation.devices = devices;

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: storageLocation,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新存储位置
  static async update(req, res, next) {
    try {
      const locationId = parseInt(req.params.id);
      const { capacity } = req.body;

      // 检查存储位置是否存在
      const existingLocation = await StorageLocation.findById(locationId);
      if (!existingLocation) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '存储位置不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const storageLocation = await StorageLocation.update(locationId, capacity);

      res.status(200).json({
        success: true,
        message: '更新成功',
        data: storageLocation,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 删除存储位置
  static async delete(req, res, next) {
    try {
      const locationId = parseInt(req.params.id);

      // 检查存储位置是否存在
      const existingLocation = await StorageLocation.findById(locationId);
      if (!existingLocation) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '存储位置不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const deleted = await StorageLocation.delete(locationId);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          error: {
            code: 'SYS_002',
            message: '内部服务器错误',
            details: '删除操作失败'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '删除成功',
        data: null,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取所有存储位置（用于下拉选择）
  static async getAll(req, res, next) {
    try {
      const storageLocations = await StorageLocation.getAll();

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: storageLocations,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = StorageLocationController;
