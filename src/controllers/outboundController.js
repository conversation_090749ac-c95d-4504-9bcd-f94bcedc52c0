const OutboundRecord = require('../models/OutboundRecord');

class OutboundController {
  // 获取出库记录列表
  static async getList(req, res, next) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      
      const filters = {};
      if (req.query.device_id) {
        filters.device_id = req.query.device_id;
      }
      if (req.query.user_id) {
        filters.user_id = parseInt(req.query.user_id);
      }
      if (req.query.start_date) {
        filters.start_date = req.query.start_date;
      }
      if (req.query.end_date) {
        filters.end_date = req.query.end_date;
      }

      const result = await OutboundRecord.getList(page, limit, filters);

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 创建出库记录
  static async create(req, res, next) {
    try {
      const { device_id, user_id, purpose, expected_return_date } = req.body;

      // 基础参数验证
      if (!device_id || !user_id) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VAL_001',
            message: '参数验证失败',
            details: '设备ID和领用人ID不能为空'
          },
          timestamp: new Date().toISOString()
        });
      }

      const record = await OutboundRecord.create(device_id, user_id, purpose, expected_return_date);

      res.status(201).json({
        success: true,
        message: '出库成功',
        data: record,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取出库记录详情
  static async getById(req, res, next) {
    try {
      const recordId = parseInt(req.params.id);

      const record = await OutboundRecord.findById(recordId);
      if (!record) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '出库记录不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: record,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 归还设备
  static async returnDevice(req, res, next) {
    try {
      const recordId = parseInt(req.params.id);
      const { notes } = req.body;

      // 检查出库记录是否存在
      const existingRecord = await OutboundRecord.findById(recordId);
      if (!existingRecord) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '出库记录不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      // 检查是否已经归还
      if (existingRecord.status === 'returned') {
        return res.status(400).json({
          success: false,
          error: {
            code: 'BIZ_002',
            message: '业务逻辑错误',
            details: '设备已归还，无法重复归还'
          },
          timestamp: new Date().toISOString()
        });
      }

      const returnedRecord = await OutboundRecord.returnDevice(recordId, notes);

      res.status(200).json({
        success: true,
        message: '归还成功',
        data: returnedRecord,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 删除出库记录（取消出库）
  static async delete(req, res, next) {
    try {
      const recordId = parseInt(req.params.id);

      // 检查出库记录是否存在
      const existingRecord = await OutboundRecord.findById(recordId);
      if (!existingRecord) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '出库记录不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const deleted = await OutboundRecord.delete(recordId);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          error: {
            code: 'SYS_002',
            message: '内部服务器错误',
            details: '取消出库操作失败'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '取消出库成功',
        data: null,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取出库统计信息
  static async getStatistics(req, res, next) {
    try {
      const stats = await OutboundRecord.getStatistics();

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = OutboundController;
