const Statistics = require('../models/Statistics');
const OutboundRecord = require('../models/OutboundRecord');

/**
 * 获取设备数量统计
 */
const getDeviceCount = async (req, res) => {
  try {
    const stats = await Statistics.getDeviceCount();
    
    res.json({
      success: true,
      data: stats,
      message: '设备数量统计获取成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取设备数量统计失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DEVICE_COUNT_STATS_ERROR',
        message: '获取设备数量统计失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取存储位置容量统计
 */
const getStorageCapacityStats = async (req, res) => {
  try {
    const stats = await Statistics.getStorageCapacityStats();
    
    res.json({
      success: true,
      data: stats,
      message: '存储位置容量统计获取成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取存储位置容量统计失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STORAGE_CAPACITY_STATS_ERROR',
        message: '获取存储位置容量统计失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取出库统计信息
 */
const getOutboundStats = async (req, res) => {
  try {
    const stats = await OutboundRecord.getStatistics();

    res.json({
      success: true,
      data: stats,
      message: '出库统计信息获取成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取出库统计信息失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'OUTBOUND_STATS_ERROR',
        message: '获取出库统计信息失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取综合统计信息
 */
const getOverviewStats = async (req, res) => {
  try {
    const [deviceStats, outboundStats] = await Promise.all([
      Statistics.getOverviewStats(),
      OutboundRecord.getStatistics()
    ]);

    const combinedStats = {
      ...deviceStats,
      outbound: outboundStats
    };

    res.json({
      success: true,
      data: combinedStats,
      message: '综合统计信息获取成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取综合统计信息失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'OVERVIEW_STATS_ERROR',
        message: '获取综合统计信息失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取所有模块的总数统计
 */
const getTotalCounts = async (req, res) => {
  try {
    const totals = await Statistics.getTotalCounts();

    res.json({
      success: true,
      data: totals,
      message: '总数统计获取成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取总数统计失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'TOTAL_COUNTS_ERROR',
        message: '获取总数统计失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  getDeviceCount,
  getStorageCapacityStats,
  getOutboundStats,
  getOverviewStats,
  getTotalCounts
};
