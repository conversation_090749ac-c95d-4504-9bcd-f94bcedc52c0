const User = require('../models/User');

class UserController {
  // 获取领用人列表
  static async getList(req, res, next) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const userName = req.query.user_name || null;

      const result = await User.getList(page, limit, userName);

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取所有领用人（用于下拉选择）
  static async getAll(req, res, next) {
    try {
      const users = await User.getAll();

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: users,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 创建领用人
  static async create(req, res, next) {
    try {
      const { name, department, contact } = req.body;

      if (!name || name.trim() === '') {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VAL_001',
            message: '参数验证失败',
            details: '领用人姓名不能为空'
          },
          timestamp: new Date().toISOString()
        });
      }

      const user = await User.create(name.trim(), department, contact);

      res.status(201).json({
        success: true,
        message: '创建成功',
        data: user,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取领用人详情
  static async getById(req, res, next) {
    try {
      const userId = parseInt(req.params.id);

      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '领用人不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: user,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新领用人
  static async update(req, res, next) {
    try {
      const userId = parseInt(req.params.id);
      const { user_name } = req.body;

      if (!user_name || user_name.trim() === '') {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VAL_001',
            message: '参数验证失败',
            details: '领用人姓名不能为空'
          },
          timestamp: new Date().toISOString()
        });
      }

      // 检查领用人是否存在
      const existingUser = await User.findById(userId);
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '领用人不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const user = await User.update(userId, user_name.trim());

      res.status(200).json({
        success: true,
        message: '更新成功',
        data: user,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 删除领用人
  static async delete(req, res, next) {
    try {
      const userId = parseInt(req.params.id);

      // 检查领用人是否存在
      const existingUser = await User.findById(userId);
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '领用人不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const deleted = await User.delete(userId);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          error: {
            code: 'SYS_002',
            message: '内部服务器错误',
            details: '删除操作失败'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '删除成功',
        data: null,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = UserController;
