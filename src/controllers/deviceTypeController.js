const DeviceType = require('../models/DeviceType');

class DeviceTypeController {
  // 获取设备类型列表
  static async getList(req, res, next) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const typeName = req.query.type_name || null;

      const result = await DeviceType.getList(page, limit, typeName);

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 创建设备类型
  static async create(req, res, next) {
    try {
      const { type_id, type_name } = req.body;

      const deviceType = await DeviceType.create(type_name, type_id);

      res.status(201).json({
        success: true,
        message: '创建成功',
        data: deviceType,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取设备类型详情
  static async getById(req, res, next) {
    try {
      const typeId = parseInt(req.params.id);

      const deviceType = await DeviceType.findById(typeId);
      if (!deviceType) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '设备类型不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: deviceType,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新设备类型
  static async update(req, res, next) {
    try {
      const typeId = parseInt(req.params.id);
      const { type_name } = req.body;

      // 检查设备类型是否存在
      const existingDeviceType = await DeviceType.findById(typeId);
      if (!existingDeviceType) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '设备类型不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const deviceType = await DeviceType.update(typeId, type_name);

      res.status(200).json({
        success: true,
        message: '更新成功',
        data: deviceType,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 删除设备类型
  static async delete(req, res, next) {
    try {
      const typeId = parseInt(req.params.id);

      // 检查设备类型是否存在
      const existingDeviceType = await DeviceType.findById(typeId);
      if (!existingDeviceType) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '设备类型不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const deleted = await DeviceType.delete(typeId);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          error: {
            code: 'SYS_002',
            message: '内部服务器错误',
            details: '删除操作失败'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '删除成功',
        data: null,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取所有设备类型（用于下拉选择）
  static async getAll(req, res, next) {
    try {
      const deviceTypes = await DeviceType.getAll();

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: deviceTypes,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = DeviceTypeController;
