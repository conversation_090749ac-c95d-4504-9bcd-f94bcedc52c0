const Device = require('../models/Device');

class <PERSON><PERSON>Controller {
  // 获取设备列表
  static async getList(req, res, next) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      
      const filters = {};
      if (req.query.device_type) {
        filters.device_type = parseInt(req.query.device_type);
      }
      if (req.query.storage_location) {
        filters.storage_location = parseInt(req.query.storage_location);
      }
      if (req.query.status) {
        filters.status = req.query.status;
      }
      if (req.query.search) {
        filters.search = req.query.search;
      }

      const result = await Device.getList(page, limit, filters);

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 创建设备
  static async create(req, res, next) {
    try {
      const { device_id, device_type, storage_location, description } = req.body;

      const device = await Device.create(device_id, device_type, storage_location, description);

      res.status(201).json({
        success: true,
        message: '创建成功',
        data: device,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取设备详情
  static async getById(req, res, next) {
    try {
      const deviceId = req.params.device_id;

      const device = await Device.findById(deviceId);
      if (!device) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '设备不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '获取成功',
        data: device,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新设备
  static async update(req, res, next) {
    try {
      const deviceId = req.params.device_id;
      const updates = req.body;

      // 检查设备是否存在
      const existingDevice = await Device.findById(deviceId);
      if (!existingDevice) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '设备不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      const device = await Device.update(deviceId, updates);

      res.status(200).json({
        success: true,
        message: '更新成功',
        data: device,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }

  // 删除设备
  static async delete(req, res, next) {
    try {
      const deviceId = req.params.device_id;

      // 检查设备是否存在
      const existingDevice = await Device.findById(deviceId);
      if (!existingDevice) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'BIZ_001',
            message: '资源不存在',
            details: '设备不存在'
          },
          timestamp: new Date().toISOString()
        });
      }

      // 检查是否有关联的出库记录
      const { query } = require('../config/database');
      const outboundRecords = await query('SELECT COUNT(*) as count FROM OutboundRecord WHERE device_id = ?', [deviceId]);

      if (outboundRecords[0].count > 0) {
        return res.status(409).json({
          success: false,
          error: {
            code: 'BIZ_004',
            message: '删除约束违反',
            details: `设备存在 ${outboundRecords[0].count} 条出库记录，请先处理相关记录后再删除设备`,
            suggestions: [
              '查看设备的出库记录',
              '删除不需要的出库记录',
              '或者考虑停用设备而不是删除'
            ]
          },
          timestamp: new Date().toISOString()
        });
      }

      const deleted = await Device.delete(deviceId);
      if (!deleted) {
        return res.status(500).json({
          success: false,
          error: {
            code: 'SYS_002',
            message: '内部服务器错误',
            details: '删除操作失败'
          },
          timestamp: new Date().toISOString()
        });
      }

      res.status(200).json({
        success: true,
        message: '删除成功',
        data: null,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = DeviceController;
