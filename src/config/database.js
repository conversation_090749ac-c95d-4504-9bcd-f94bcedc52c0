const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置 - MySQL2兼容版本
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  // 连接池配置
  waitForConnections: true,
  connectionLimit: 5,            // 减少连接池以节省内存
  queueLimit: 0,
  // MySQL2有效配置
  charset: 'utf8mb4',            // 字符集
  timezone: '+08:00',            // 时区
  supportBigNumbers: true,       // 支持大数字
  bigNumberStrings: true,        // 大数字返回字符串
  dateStrings: false,            // 日期不返回字符串
  debug: false,                  // 关闭调试模式
  multipleStatements: false      // 禁用多语句查询
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 缓存数据库连接状态
let lastConnectionCheck = 0;
let lastConnectionStatus = false;
const CONNECTION_CHECK_INTERVAL = 30000; // 30秒检查一次

// 测试数据库连接（带缓存）
const testConnection = async (forceCheck = false) => {
  const now = Date.now();

  // 如果不是强制检查且在缓存时间内，返回缓存结果
  if (!forceCheck && (now - lastConnectionCheck) < CONNECTION_CHECK_INTERVAL) {
    return lastConnectionStatus;
  }

  try {
    const connection = await pool.getConnection();
    await connection.ping(); // 使用ping检查连接
    connection.release();

    lastConnectionCheck = now;
    lastConnectionStatus = true;
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    lastConnectionCheck = now;
    lastConnectionStatus = false;
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
};

// 执行查询的通用方法
const query = async (sql, params = []) => {
  const startTime = Date.now();

  try {
    // 确保参数是数组且所有参数都是有效值
    const cleanParams = params.map(param => {
      if (param === null || param === undefined) {
        return null;
      }
      return param;
    });

    // 只在开发环境打印详细SQL日志
    if (process.env.NODE_ENV !== 'production') {
      console.log('执行SQL:', sql);
      console.log('参数:', cleanParams);
    }

    const [rows] = await pool.execute(sql, cleanParams);

    const duration = Date.now() - startTime;
    if (duration > 1000) { // 只记录慢查询
      console.warn(`慢查询警告: ${duration}ms - ${sql.substring(0, 100)}...`);
    }

    return rows;
  } catch (error) {
    console.error('数据库查询错误:', error);
    console.error('SQL:', sql);
    console.error('参数:', params);
    throw error;
  }
};

// 执行事务
const transaction = async (callback) => {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

module.exports = {
  pool,
  query,
  transaction,
  testConnection
};
