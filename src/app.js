const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 导入配置和中间件
const { testConnection } = require('./config/database');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const routes = require('./routes');

const app = express();
const PORT = process.env.PORT || 8080;

// 信任代理设置（微信云托管需要）
app.set('trust proxy', true);

// Express优化配置
app.set('x-powered-by', false);  // 隐藏Express标识
app.set('etag', 'weak');         // 启用弱ETag

// 安全中间件
app.use(helmet());

// CORS配置
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  credentials: true
}));

// 限流配置
// const limiter = rateLimit({
//   windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 5 * 60 * 1000, // 5分钟
//   max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 1000, // 限制每个IP 5分钟内最多1000个请求
//   trustProxy: false, // 明确禁用速率限制的代理信任，避免ERR_ERL_PERMISSIVE_TRUST_PROXY错误
//   message: {
//     success: false,
//     error: {
//       code: 'RATE_LIMIT',
//       message: '请求过于频繁',
//       details: '请稍后再试'
//     },
//     timestamp: new Date().toISOString()
//   },
//   standardHeaders: true,
//   legacyHeaders: false
// });

// app.use(limiter);

// 解析JSON请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip}`);
  next();
});

// 私有访问控制中间件（仅对API路由生效）
const privateAccessControl = (req, res, next) => {
  // 如果未启用访问控制，直接通过
  if (process.env.ENABLE_ACCESS_CONTROL !== 'true') {
    return next();
  }

  // 检查访问密钥
  const apiKey = req.headers['x-api-key'] || req.query.api_key;
  const expectedKey = process.env.API_ACCESS_KEY;

  if (!apiKey || apiKey !== expectedKey) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTH_001',
        message: '访问被拒绝',
        details: '需要有效的API访问密钥'
      },
      timestamp: new Date().toISOString()
    });
  }

  next();
};

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    // 设置响应头优化 - 避免与nginx重复
    res.set({
      'X-Health-Check': 'true',
      'X-Server-Time': new Date().toISOString()
    });

    const dbConnected = await testConnection();
    res.status(200).json({
      success: true,
      message: 'API服务器运行正常',
      data: {
        status: 'healthy',
        database: dbConnected ? 'connected' : 'disconnected',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0'
      }
    });
  } catch (error) {
    console.error('健康检查失败:', error);
    res.status(503).json({
      success: false,
      error: {
        code: 'SYS_003',
        message: '服务暂时不可用',
        details: '健康检查失败'
      },
      timestamp: new Date().toISOString()
    });
  }
});

// API路由
app.use('/api/v1', routes);

// 404处理
app.use(notFoundHandler);

// 全局错误处理
app.use(errorHandler);

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    app.listen(PORT, () => {
      console.log('🚀 AnxinlaDB API服务器启动成功');
      console.log(`📍 服务器地址: http://localhost:${PORT}`);
      console.log(`🔗 API文档: http://localhost:${PORT}/api/v1`);
      console.log(`💚 健康检查: http://localhost:${PORT}/health`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log('=' .repeat(50));
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在优雅关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在优雅关闭服务器...');
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
startServer();

module.exports = app;
