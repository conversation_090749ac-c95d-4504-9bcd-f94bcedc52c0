const Joi = require('joi');

// 通用验证中间件
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'PARAM_002',
          message: '参数格式错误',
          details: error.details[0].message
        },
        timestamp: new Date().toISOString()
      });
    }
    next();
  };
};

// 查询参数验证中间件
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.query);
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'PARAM_002',
          message: '查询参数格式错误',
          details: error.details[0].message
        },
        timestamp: new Date().toISOString()
      });
    }
    next();
  };
};

// 路径参数验证中间件
const validateParams = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.params);
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'PARAM_002',
          message: '路径参数格式错误',
          details: error.details[0].message
        },
        timestamp: new Date().toISOString()
      });
    }
    next();
  };
};

// 验证模式定义
const schemas = {

  // 设备类型
  deviceType: Joi.object({
    type_id: Joi.number().integer().min(1).max(99999).optional().messages({
      'number.min': '设备类型ID必须是正整数',
      'number.max': '设备类型ID不能超过5位数',
      'number.integer': '设备类型ID必须是整数'
    }),
    type_name: Joi.string().max(50).required().messages({
      'string.max': '设备类型名称不能超过50个字符',
      'any.required': '设备类型名称是必需的'
    })
  }),

  // 存储位置
  storageLocation: Joi.object({
    location_id: Joi.number().integer().min(1).max(99999).optional().messages({
      'number.min': '存储位置ID必须是正整数',
      'number.max': '存储位置ID不能超过5位数',
      'number.integer': '存储位置ID必须是整数'
    }),
    capacity: Joi.number().integer().min(0).required().messages({
      'number.min': '容量不能为负数',
      'any.required': '容量是必需的'
    })
  }),

  // 设备
  device: Joi.object({
    device_id: Joi.string().pattern(/^[a-zA-Z0-9]+$/).required().messages({
      'string.pattern.base': '设备ID只能包含字母和数字，不能有空格或其他特殊字符',
      'any.required': '设备ID是必需的'
    }),
    device_type: Joi.number().integer().positive().required().messages({
      'number.positive': '设备类型ID必须是正整数',
      'any.required': '设备类型是必需的'
    }),
    storage_location: Joi.number().integer().positive().required().messages({
      'number.positive': '存储位置ID必须是正整数',
      'any.required': '存储位置是必需的'
    }),
    description: Joi.string().allow('', null).optional()
  }),

  // 设备更新
  deviceUpdate: Joi.object({
    device_type: Joi.number().integer().positive().optional().messages({
      'number.positive': '设备类型ID必须是正整数'
    }),
    storage_location: Joi.number().integer().positive().optional().messages({
      'number.positive': '存储位置ID必须是正整数'
    }),
    description: Joi.string().allow('', null).optional()
  }),

  // 分页查询参数
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).max(100).default(10).optional()
  }),

  // 设备类型查询参数
  deviceTypeQuery: Joi.object({
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).max(100).default(10).optional(),
    type_name: Joi.string().optional()
  }),

  // 存储位置查询参数
  storageLocationQuery: Joi.object({
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).max(100).default(10).optional(),
    min_capacity: Joi.number().integer().min(0).optional(),
    max_capacity: Joi.number().integer().min(0).optional()
  }),

  // 设备查询参数
  deviceQuery: Joi.object({
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).max(100).default(10).optional(),
    device_type: Joi.number().integer().positive().optional(),
    storage_location: Joi.number().integer().positive().optional(),
    status: Joi.string().valid('available', 'outbound').optional().messages({
      'any.only': 'status参数只能是available或outbound'
    }),
    search: Joi.string().optional()
  }),

  // ID参数
  idParam: Joi.object({
    id: Joi.number().integer().positive().required().messages({
      'number.positive': 'ID必须是正整数',
      'any.required': 'ID是必需的'
    })
  }),

  // 设备ID参数
  deviceIdParam: Joi.object({
    device_id: Joi.string().pattern(/^[a-zA-Z0-9]+$/).required().messages({
      'string.pattern.base': '设备ID只能包含字母和数字，不能有空格或其他特殊字符',
      'any.required': '设备ID是必需的'
    })
  })
};

module.exports = {
  validate,
  validateQuery,
  validateParams,
  schemas
};
