// 全局错误处理中间件
const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', err);

  // 默认错误响应
  let statusCode = 500;
  let errorResponse = {
    success: false,
    error: {
      code: 'SYS_002',
      message: '内部服务器错误',
      details: '服务器处理请求时发生错误'
    },
    timestamp: new Date().toISOString()
  };

  // MySQL错误处理
  if (err.code) {
    switch (err.code) {
      case 'ER_DUP_ENTRY':
        statusCode = 400;
        errorResponse.error = {
          code: 'BIZ_002',
          message: '资源已存在',
          details: '该记录已存在，无法重复创建'
        };
        break;
      
      case 'ER_NO_REFERENCED_ROW_2':
        statusCode = 400;
        errorResponse.error = {
          code: 'BIZ_003',
          message: '外键约束违反',
          details: '关联的资源不存在'
        };
        break;
      
      case 'ER_ROW_IS_REFERENCED_2':
        statusCode = 409;
        errorResponse.error = {
          code: 'BIZ_004',
          message: '删除约束违反',
          details: '存在关联数据，无法删除'
        };
        break;
      
      case 'ECONNREFUSED':
        statusCode = 503;
        errorResponse.error = {
          code: 'SYS_001',
          message: '数据库连接错误',
          details: '无法连接到数据库服务器'
        };
        break;
      
      case 'PROTOCOL_CONNECTION_LOST':
        statusCode = 503;
        errorResponse.error = {
          code: 'SYS_001',
          message: '数据库连接错误',
          details: '数据库连接已断开'
        };
        break;
    }
  }

  // 业务逻辑错误处理
  if (err.message) {
    if (err.message.includes('不存在')) {
      statusCode = 404;
      errorResponse.error = {
        code: 'BIZ_001',
        message: '资源不存在',
        details: err.message
      };
    } else if (err.message.includes('已存在')) {
      statusCode = 400;
      errorResponse.error = {
        code: 'BIZ_002',
        message: '资源已存在',
        details: err.message
      };
    } else if (err.message.includes('容量不足')) {
      statusCode = 400;
      errorResponse.error = {
        code: 'BIZ_005',
        message: '存储位置容量不足',
        details: err.message
      };
    } else if (err.message.includes('容量') && err.message.includes('小于')) {
      statusCode = 400;
      errorResponse.error = {
        code: 'BIZ_006',
        message: '容量更新失败',
        details: err.message
      };
    } else if (err.message.includes('格式错误')) {
      statusCode = 400;
      errorResponse.error = {
        code: 'PARAM_004',
        message: '参数格式错误',
        details: err.message
      };
    } else if (err.message.includes('令牌')) {
      statusCode = 401;
      errorResponse.error = {
        code: 'AUTH_002',
        message: '令牌无效或已过期',
        details: err.message
      };
    } else if (err.message.includes('无法删除')) {
      statusCode = 409;
      errorResponse.error = {
        code: 'BIZ_004',
        message: '删除约束违反',
        details: err.message
      };
    }
  }

  // JWT错误处理
  if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    errorResponse.error = {
      code: 'AUTH_004',
      message: '令牌格式错误',
      details: '提供的JWT令牌格式不正确'
    };
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    errorResponse.error = {
      code: 'AUTH_002',
      message: '令牌无效或已过期',
      details: 'JWT令牌已过期'
    };
  }

  // 开发环境下显示详细错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = err.stack;
  }

  res.status(statusCode).json(errorResponse);
};

// 404错误处理中间件
const notFoundHandler = (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'BIZ_001',
      message: '资源不存在',
      details: `路径 ${req.originalUrl} 不存在`
    },
    timestamp: new Date().toISOString()
  });
};

module.exports = {
  errorHandler,
  notFoundHandler
};
