const { query } = require('../config/database');

// 初始化数据库数据
const initDatabase = async () => {
  try {
    console.log('🔄 开始初始化数据库数据...');

    // 检查设备类型是否已存在
    const deviceTypeCheck = await query('SELECT COUNT(*) as count FROM DeviceType');
    if (deviceTypeCheck[0].count === 0) {
      // 创建默认设备类型
      await query('INSERT INTO DeviceType (type_name) VALUES (?)', ['Computer']);
      await query('INSERT INTO DeviceType (type_name) VALUES (?)', ['Printer']);
      console.log('✅ 默认设备类型创建成功 (Computer, Printer)');
    } else {
      console.log('ℹ️  设备类型已存在，跳过创建');
    }

    // 检查存储位置是否已存在
    const storageLocationCheck = await query('SELECT COUNT(*) as count FROM StorageLocation');
    if (storageLocationCheck[0].count === 0) {
      // 创建默认存储位置
      await query('INSERT INTO StorageLocation (capacity) VALUES (?)', [100]);
      await query('INSERT INTO StorageLocation (capacity) VALUES (?)', [50]);
      console.log('✅ 默认存储位置创建成功 (容量: 100, 50)');
    } else {
      console.log('ℹ️  存储位置已存在，跳过创建');
    }

    // 检查设备是否已存在
    const deviceCheck = await query('SELECT COUNT(*) as count FROM Device');
    if (deviceCheck[0].count === 0) {
      // 创建默认设备
      await query(
        'INSERT INTO Device (device_id, device_type, storage_location, description) VALUES (?, ?, ?, ?)',
        ['DEV001XYZ0123456789A', 1, 1, '主办公室电脑']
      );
      console.log('✅ 默认设备创建成功 (DEV001XYZ0123456789A)');
    } else {
      console.log('ℹ️  设备已存在，跳过创建');
    }

    console.log('🎉 数据库初始化完成！');
    return true;
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    return false;
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('初始化失败:', error);
    process.exit(1);
  });
}

module.exports = { initDatabase };
