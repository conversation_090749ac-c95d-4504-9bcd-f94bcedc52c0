const express = require('express');
const router = express.Router();
const StorageLocationController = require('../controllers/storageLocationController');
const { validate, validateQuery, validateParams, schemas } = require('../middleware/validation');

// 获取存储位置列表
router.get('/', validateQuery(schemas.storageLocationQuery), StorageLocationController.getList);

// 获取所有存储位置（用于下拉选择）
router.get('/all', StorageLocationController.getAll);

// 创建存储位置
router.post('/', validate(schemas.storageLocation), StorageLocationController.create);

// 获取存储位置详情
router.get('/:id', validateParams(schemas.idParam), StorageLocationController.getById);

// 更新存储位置
router.put('/:id', validateParams(schemas.idParam), validate(schemas.storageLocation), StorageLocationController.update);

// 删除存储位置
router.delete('/:id', validateParams(schemas.idParam), StorageLocationController.delete);

module.exports = router;
