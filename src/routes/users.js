const express = require('express');
const router = express.Router();
const UserController = require('../controllers/userController');

// 获取领用人列表
router.get('/', UserController.getList);

// 获取所有领用人（用于下拉选择）
router.get('/all', UserController.getAll);

// 创建领用人
router.post('/', UserController.create);

// 获取领用人详情
router.get('/:id', UserController.getById);

// 更新领用人
router.put('/:id', UserController.update);

// 删除领用人
router.delete('/:id', UserController.delete);

module.exports = router;
