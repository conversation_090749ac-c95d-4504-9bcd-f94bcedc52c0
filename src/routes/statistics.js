const express = require('express');
const router = express.Router();
const statisticsController = require('../controllers/statisticsController');

/**
 * @route GET /api/v1/statistics/overview
 * @desc 获取综合统计信息（设备数量 + 存储容量统计）
 * @access Public
 * @returns {Object} 综合统计信息
 */
router.get('/overview', statisticsController.getOverviewStats);

/**
 * @route GET /api/v1/statistics/devices
 * @desc 获取设备数量统计
 * @access Public
 * @returns {Object} 设备数量统计
 */
router.get('/devices', statisticsController.getDeviceCount);

/**
 * @route GET /api/v1/statistics/storage-capacity
 * @desc 获取存储位置容量统计
 * @access Public
 * @returns {Object} 存储位置容量统计
 */
router.get('/storage-capacity', statisticsController.getStorageCapacityStats);

/**
 * @route GET /api/v1/statistics/outbound
 * @desc 获取出库统计信息
 * @access Public
 * @returns {Object} 出库统计信息
 */
router.get('/outbound', statisticsController.getOutboundStats);

/**
 * @route GET /api/v1/statistics/totals
 * @desc 获取所有模块的总数统计
 * @access Public
 * @returns {Object} 总数统计信息
 */
router.get('/totals', statisticsController.getTotalCounts);

module.exports = router;
