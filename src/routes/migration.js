const express = require('express');
const router = express.Router();
const { query, testConnection } = require('../config/database');

// 检查表是否存在
const tableExists = async (tableName) => {
  try {
    const result = await query('SHOW TABLES LIKE ?', [tableName]);
    return result.length > 0;
  } catch (error) {
    return false;
  }
};

// 检查列是否存在
const columnExists = async (tableName, columnName) => {
  try {
    const result = await query('SHOW COLUMNS FROM ?? LIKE ?', [tableName, columnName]);
    return result.length > 0;
  } catch (error) {
    return false;
  }
};

// 执行出库功能迁移的API端点
router.post('/outbound', async (req, res) => {
  try {
    console.log('🔄 开始执行出库功能数据库迁移...');
    
    const migrationResults = [];

    // 1. 为Device表添加status字段
    const statusExists = await columnExists('Device', 'status');
    if (!statusExists) {
      console.log('📝 为Device表添加status字段...');
      await query(`
        ALTER TABLE Device 
        ADD COLUMN status ENUM('available', 'outbound') DEFAULT 'available'
        AFTER description
      `);
      await query('ALTER TABLE Device ADD INDEX idx_status (status)');
      migrationResults.push('✅ Device表status字段添加成功');
    } else {
      migrationResults.push('ℹ️ Device表status字段已存在');
    }

    // 2. 创建User表（领用人）
    const userTableExists = await tableExists('User');
    if (!userTableExists) {
      console.log('📝 创建User表...');
      await query(`
        CREATE TABLE User (
          user_id INT AUTO_INCREMENT PRIMARY KEY,
          user_name VARCHAR(100) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_user_name (user_name)
        )
      `);
      migrationResults.push('✅ User表创建成功');
    } else {
      migrationResults.push('ℹ️ User表已存在');
    }

    // 3. 创建OutboundRecord表（出库记录）
    const outboundTableExists = await tableExists('OutboundRecord');
    if (!outboundTableExists) {
      console.log('📝 创建OutboundRecord表...');
      await query(`
        CREATE TABLE OutboundRecord (
          record_id INT AUTO_INCREMENT PRIMARY KEY,
          device_id VARCHAR(20) NOT NULL,
          user_id INT NOT NULL,
          description TEXT,
          outbound_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (device_id) REFERENCES Device(device_id) ON DELETE RESTRICT ON UPDATE CASCADE,
          FOREIGN KEY (user_id) REFERENCES User(user_id) ON DELETE RESTRICT ON UPDATE CASCADE,
          INDEX idx_device_id (device_id),
          INDEX idx_user_id (user_id),
          INDEX idx_outbound_time (outbound_time)
        )
      `);
      migrationResults.push('✅ OutboundRecord表创建成功');
    } else {
      migrationResults.push('ℹ️ OutboundRecord表已存在');
    }

    // 4. 创建示例数据
    const userCount = await query('SELECT COUNT(*) as count FROM User');
    if (userCount[0].count === 0) {
      console.log('📝 创建示例领用人...');
      await query('INSERT INTO User (user_name) VALUES (?)', ['张三']);
      await query('INSERT INTO User (user_name) VALUES (?)', ['李四']);
      migrationResults.push('✅ 示例领用人创建成功');
    } else {
      migrationResults.push('ℹ️ 领用人数据已存在');
    }

    console.log('🎉 出库功能迁移完成！');

    res.status(200).json({
      success: true,
      message: '出库功能数据库迁移完成',
      data: {
        results: migrationResults,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'MIGRATION_ERROR',
        message: '数据库迁移失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
});

// 检查迁移状态的API端点
router.get('/status', async (req, res) => {
  try {
    const status = {
      database_connected: false,
      device_status_field: false,
      user_table: false,
      outbound_table: false,
      migration_complete: false
    };

    // 检查数据库连接
    status.database_connected = await testConnection();

    if (status.database_connected) {
      // 检查Device表的status字段
      status.device_status_field = await columnExists('Device', 'status');
      
      // 检查User表
      status.user_table = await tableExists('User');
      
      // 检查OutboundRecord表
      status.outbound_table = await tableExists('OutboundRecord');
      
      // 判断迁移是否完成
      status.migration_complete = status.device_status_field && status.user_table && status.outbound_table;
    }

    res.status(200).json({
      success: true,
      message: '迁移状态检查完成',
      data: status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 状态检查失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STATUS_CHECK_ERROR',
        message: '迁移状态检查失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
