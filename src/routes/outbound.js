const express = require('express');
const router = express.Router();
const OutboundController = require('../controllers/outboundController');

// 获取出库记录列表
router.get('/', OutboundController.getList);

// 创建出库记录
router.post('/', OutboundController.create);

// 获取出库记录详情
router.get('/:id', OutboundController.getById);

// 归还设备
router.put('/:id/return', OutboundController.returnDevice);

// 删除出库记录（取消出库）
router.delete('/:id', OutboundController.delete);

// 获取出库统计信息
router.get('/statistics', OutboundController.getStatistics);

module.exports = router;
