const express = require('express');
const router = express.Router();
const DeviceController = require('../controllers/deviceController');
const { validate, validateQuery, validateParams, schemas } = require('../middleware/validation');

// 获取设备列表
router.get('/', validateQuery(schemas.deviceQuery), DeviceController.getList);

// 创建设备
router.post('/', validate(schemas.device), DeviceController.create);

// 获取设备详情
router.get('/:device_id', validateParams(schemas.deviceIdParam), DeviceController.getById);

// 更新设备
router.put('/:device_id', validateParams(schemas.deviceIdParam), validate(schemas.deviceUpdate), DeviceController.update);

// 删除设备
router.delete('/:device_id', validateParams(schemas.deviceIdParam), DeviceController.delete);

module.exports = router;
