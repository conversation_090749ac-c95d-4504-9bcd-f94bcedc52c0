const express = require('express');
const router = express.Router();
const DeviceTypeController = require('../controllers/deviceTypeController');
const { validate, validateQuery, validateParams, schemas } = require('../middleware/validation');

// 获取设备类型列表
router.get('/', validateQuery(schemas.deviceTypeQuery), DeviceTypeController.getList);

// 获取所有设备类型（用于下拉选择）
router.get('/all', DeviceTypeController.getAll);

// 创建设备类型
router.post('/', validate(schemas.deviceType), DeviceTypeController.create);

// 获取设备类型详情
router.get('/:id', validateParams(schemas.idParam), DeviceTypeController.getById);

// 更新设备类型
router.put('/:id', validateParams(schemas.idParam), validate(schemas.deviceType), DeviceTypeController.update);

// 删除设备类型
router.delete('/:id', validateParams(schemas.idParam), DeviceTypeController.delete);

module.exports = router;
