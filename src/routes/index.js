const express = require('express');
const router = express.Router();

// 导入各个路由模块
const deviceTypeRoutes = require('./deviceTypes');
const storageLocationRoutes = require('./storageLocations');
const deviceRoutes = require('./devices');
const statisticsRoutes = require('./statistics');
const userRoutes = require('./users');
const outboundRoutes = require('./outbound');

// API根路径信息
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'AnxinlaDB API Server',
    version: '1.0.0',
    description: '设备管理系统后端API服务器',
    documentation: {
      health_check: 'GET /health',
      api_info: 'GET /api/v1'
    },
    endpoints: {
      device_types: '/api/v1/device-types',
      storage_locations: '/api/v1/storage-locations',
      devices: '/api/v1/devices',
      users: '/api/v1/users',
      outbound: '/api/v1/outbound',
      statistics: '/api/v1/statistics'
    },
    features: [
      '设备类型管理',
      '存储位置管理',
      '设备管理',
      '领用人管理',
      '出库管理',
      '基础统计',
      '分页查询',
      '数据验证',
      '错误处理'
    ],
    timestamp: new Date().toISOString()
  });
});

// 注册路由
router.use('/device-types', deviceTypeRoutes);
router.use('/storage-locations', storageLocationRoutes);
router.use('/devices', deviceRoutes);
router.use('/users', userRoutes);
router.use('/outbound', outboundRoutes);
router.use('/statistics', statisticsRoutes);

module.exports = router;
