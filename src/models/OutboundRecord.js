const { query } = require('../config/database');
const Device = require('./Device');
const User = require('./User');

class OutboundRecord {
  // 获取出库记录列表
  static async getList(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let sql = `
      SELECT
        o.*,
        d.device_id,
        dt.type_name as device_type_name,
        u.user_name as user_name
      FROM OutboundRecord o
      JOIN Device d ON o.device_id = d.device_id
      JOIN DeviceType dt ON d.device_type = dt.type_id
      JOIN User u ON o.user_id = u.user_id
    `;
    
    let countSql = `
      SELECT COUNT(*) as total
      FROM OutboundRecord o
      JOIN Device d ON o.device_id = d.device_id
      JOIN User u ON o.user_id = u.user_id
    `;
    
    const params = [];
    const countParams = [];
    const conditions = [];

    // 按设备ID筛选
    if (filters.device_id) {
      conditions.push('o.device_id = ?');
      params.push(filters.device_id);
      countParams.push(filters.device_id);
    }

    // 按领用人筛选
    if (filters.user_id) {
      conditions.push('o.user_id = ?');
      params.push(filters.user_id);
      countParams.push(filters.user_id);
    }

    // 按时间范围筛选
    if (filters.start_date) {
      conditions.push('DATE(o.outbound_time) >= ?');
      params.push(filters.start_date);
      countParams.push(filters.start_date);
    }

    if (filters.end_date) {
      conditions.push('DATE(o.outbound_time) <= ?');
      params.push(filters.end_date);
      countParams.push(filters.end_date);
    }

    if (conditions.length > 0) {
      const whereClause = ' WHERE ' + conditions.join(' AND ');
      sql += whereClause;
      countSql += whereClause;
    }

    sql += ` ORDER BY o.outbound_time DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const [items, totalResult] = await Promise.all([
      query(sql, params),
      query(countSql, countParams)
    ]);

    return {
      items,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalResult[0].total,
        pages: Math.ceil(totalResult[0].total / limit)
      }
    };
  }

  // 根据ID获取出库记录详情
  static async findById(recordId) {
    const sql = `
      SELECT
        o.*,
        d.device_id,
        dt.type_name as device_type_name,
        sl.location_id as storage_location_id,
        u.user_name as user_name
      FROM OutboundRecord o
      JOIN Device d ON o.device_id = d.device_id
      JOIN DeviceType dt ON d.device_type = dt.type_id
      JOIN StorageLocation sl ON d.storage_location = sl.location_id
      JOIN User u ON o.user_id = u.user_id
      WHERE o.record_id = ?
    `;
    const result = await query(sql, [recordId]);
    return result[0] || null;
  }

  // 创建出库记录
  static async create(deviceId, userId, purpose = null, expectedReturnDate = null) {
    // 验证设备是否存在且可出库
    const device = await Device.findById(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    if (device.status === 'outbound') {
      throw new Error('设备已出库，无法重复出库');
    }

    // 验证领用人是否存在
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('领用人不存在');
    }

    // 使用事务确保数据一致性
    const connection = await require('../config/database').pool.getConnection();
    try {
      await connection.beginTransaction();

      // 创建出库记录
      const insertSql = 'INSERT INTO OutboundRecord (device_id, user_id, purpose, expected_return_date) VALUES (?, ?, ?, ?)';
      const insertResult = await connection.execute(insertSql, [deviceId, userId, purpose, expectedReturnDate]);

      // 更新设备状态为已出库
      const updateSql = 'UPDATE Device SET status = "outbound" WHERE device_id = ?';
      await connection.execute(updateSql, [deviceId]);

      await connection.commit();
      
      return await this.findById(insertResult[0].insertId);
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 归还设备
  static async returnDevice(recordId, notes = null) {
    const record = await this.findById(recordId);
    if (!record) {
      throw new Error('出库记录不存在');
    }

    if (record.status === 'returned') {
      throw new Error('设备已归还，无法重复归还');
    }

    // 使用事务确保数据一致性
    const connection = await require('../config/database').pool.getConnection();
    try {
      await connection.beginTransaction();

      // 更新出库记录状态为已归还
      const updateRecordSql = `
        UPDATE OutboundRecord
        SET status = 'returned',
            actual_return_date = CURDATE(),
            notes = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE record_id = ?
      `;
      await connection.execute(updateRecordSql, [notes, recordId]);

      // 恢复设备状态为可用
      const updateDeviceSql = 'UPDATE Device SET status = "available" WHERE device_id = ?';
      await connection.execute(updateDeviceSql, [record.device_id]);

      await connection.commit();

      return await this.findById(recordId);
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 删除出库记录（取消出库）
  static async delete(recordId) {
    const record = await this.findById(recordId);
    if (!record) {
      throw new Error('出库记录不存在');
    }

    // 使用事务确保数据一致性
    const connection = await require('../config/database').pool.getConnection();
    try {
      await connection.beginTransaction();

      // 删除出库记录
      const deleteSql = 'DELETE FROM OutboundRecord WHERE record_id = ?';
      await connection.execute(deleteSql, [recordId]);

      // 恢复设备状态为可用
      const updateSql = 'UPDATE Device SET status = "available" WHERE device_id = ?';
      await connection.execute(updateSql, [record.device_id]);

      await connection.commit();
      return true;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 获取出库统计信息
  static async getStatistics() {
    const totalSql = 'SELECT COUNT(*) as total FROM OutboundRecord';
    const todaySql = 'SELECT COUNT(*) as today FROM OutboundRecord WHERE DATE(outbound_time) = CURDATE()';
    const byUserSql = `
      SELECT u.user_name as user_name, COUNT(*) as count
      FROM OutboundRecord o
      JOIN User u ON o.user_id = u.user_id
      GROUP BY o.user_id
      ORDER BY count DESC
      LIMIT 5
    `;

    const [totalResult, todayResult, byUserResult] = await Promise.all([
      query(totalSql),
      query(todaySql),
      query(byUserSql)
    ]);

    return {
      total: totalResult[0].total,
      today: todayResult[0].today,
      by_user: byUserResult
    };
  }
}

module.exports = OutboundRecord;
