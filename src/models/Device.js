const { query } = require('../config/database');
const StorageLocation = require('./StorageLocation');
const DeviceType = require('./DeviceType');

class Device {
  // 获取设备列表（支持分页和多条件过滤）
  static async getList(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let sql = `
      SELECT 
        d.*,
        dt.type_name,
        sl.capacity as storage_capacity,
        COALESCE(device_count.count, 0) as storage_current_usage,
        (sl.capacity - COALESCE(device_count.count, 0)) as storage_available_capacity
      FROM Device d
      JOIN DeviceType dt ON d.device_type = dt.type_id
      JOIN StorageLocation sl ON d.storage_location = sl.location_id
      LEFT JOIN (
        SELECT storage_location, COUNT(*) as count 
        FROM Device 
        GROUP BY storage_location
      ) device_count ON sl.location_id = device_count.storage_location
    `;
    
    let countSql = `
      SELECT COUNT(*) as total 
      FROM Device d
      JOIN DeviceType dt ON d.device_type = dt.type_id
      JOIN StorageLocation sl ON d.storage_location = sl.location_id
    `;
    
    const params = [];
    const countParams = [];
    const conditions = [];

    // 按设备类型过滤
    if (filters.device_type) {
      conditions.push('d.device_type = ?');
      params.push(filters.device_type);
      countParams.push(filters.device_type);
    }

    // 按存储位置过滤
    if (filters.storage_location) {
      conditions.push('d.storage_location = ?');
      params.push(filters.storage_location);
      countParams.push(filters.storage_location);
    }

    // 按设备状态过滤
    if (filters.status) {
      conditions.push('d.status = ?');
      params.push(filters.status);
      countParams.push(filters.status);
    }

    // 按设备ID或描述搜索
    if (filters.search) {
      conditions.push('(d.device_id LIKE ? OR d.description LIKE ?)');
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
      countParams.push(searchTerm, searchTerm);
    }

    if (conditions.length > 0) {
      const whereClause = ' WHERE ' + conditions.join(' AND ');
      sql += whereClause;
      countSql += whereClause;
    }

    sql += ` ORDER BY d.created_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const [items, totalResult] = await Promise.all([
      query(sql, params),
      query(countSql, countParams)
    ]);

    // 格式化返回数据
    const formattedItems = items.map(item => ({
      device_id: item.device_id,
      device_type: {
        type_id: item.device_type,
        type_name: item.type_name
      },
      storage_location: {
        location_id: item.storage_location,
        capacity: item.storage_capacity,
        available_capacity: item.storage_available_capacity
      },
      description: item.description,
      status: item.status || 'available', // 添加状态字段
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    const total = totalResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      items: formattedItems,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: total,
        items_per_page: limit
      }
    };
  }

  // 根据ID获取设备详情
  static async findById(deviceId) {
    const sql = `
      SELECT 
        d.*,
        dt.type_name,
        sl.capacity as storage_capacity,
        COALESCE(device_count.count, 0) as storage_current_usage,
        (sl.capacity - COALESCE(device_count.count, 0)) as storage_available_capacity
      FROM Device d
      JOIN DeviceType dt ON d.device_type = dt.type_id
      JOIN StorageLocation sl ON d.storage_location = sl.location_id
      LEFT JOIN (
        SELECT storage_location, COUNT(*) as count 
        FROM Device 
        GROUP BY storage_location
      ) device_count ON sl.location_id = device_count.storage_location
      WHERE d.device_id = ?
    `;
    
    const result = await query(sql, [deviceId]);
    if (!result[0]) return null;

    const item = result[0];
    return {
      device_id: item.device_id,
      device_type: {
        type_id: item.device_type,
        type_name: item.type_name
      },
      storage_location: {
        location_id: item.storage_location,
        capacity: item.storage_capacity,
        current_usage: item.storage_current_usage,
        available_capacity: item.storage_available_capacity
      },
      description: item.description,
      status: item.status || 'available', // 添加状态字段
      created_at: item.created_at,
      updated_at: item.updated_at
    };
  }

  // 创建设备
  static async create(deviceId, deviceType, storageLocation, description = null) {
    // 验证设备ID格式
    if (!this.validateDeviceId(deviceId)) {
      throw new Error('设备ID格式错误，只能包含字母和数字，不能有空格或其他特殊字符');
    }

    // 检查设备ID是否已存在
    const existing = await this.findById(deviceId);
    if (existing) {
      throw new Error('设备ID已存在');
    }

    // 验证设备类型是否存在
    const deviceTypeObj = await DeviceType.findById(deviceType);
    if (!deviceTypeObj) {
      throw new Error('设备类型不存在');
    }

    // 验证存储位置是否存在且容量足够
    const storageLocationObj = await StorageLocation.findById(storageLocation);
    if (!storageLocationObj) {
      throw new Error('存储位置不存在');
    }

    if (storageLocationObj.available_capacity < 1) {
      throw new Error('存储位置容量不足');
    }

    const sql = 'INSERT INTO Device (device_id, device_type, storage_location, description) VALUES (?, ?, ?, ?)';
    await query(sql, [deviceId, deviceType, storageLocation, description]);
    return await this.findById(deviceId);
  }

  // 更新设备
  static async update(deviceId, updates) {
    const device = await this.findById(deviceId);
    if (!device) {
      throw new Error('设备不存在');
    }

    const updateFields = [];
    const params = [];

    if (updates.device_type !== undefined) {
      const deviceTypeObj = await DeviceType.findById(updates.device_type);
      if (!deviceTypeObj) {
        throw new Error('设备类型不存在');
      }
      updateFields.push('device_type = ?');
      params.push(updates.device_type);
    }

    if (updates.storage_location !== undefined) {
      const storageLocationObj = await StorageLocation.findById(updates.storage_location);
      if (!storageLocationObj) {
        throw new Error('存储位置不存在');
      }

      // 如果更换存储位置，检查新位置容量
      if (updates.storage_location !== device.storage_location.location_id) {
        if (storageLocationObj.available_capacity < 1) {
          throw new Error('目标存储位置容量不足');
        }
      }

      updateFields.push('storage_location = ?');
      params.push(updates.storage_location);
    }

    if (updates.description !== undefined) {
      updateFields.push('description = ?');
      params.push(updates.description);
    }

    if (updateFields.length === 0) {
      return device;
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(deviceId);

    const sql = `UPDATE Device SET ${updateFields.join(', ')} WHERE device_id = ?`;
    await query(sql, params);
    return await this.findById(deviceId);
  }

  // 删除设备
  static async delete(deviceId) {
    const sql = 'DELETE FROM Device WHERE device_id = ?';
    const result = await query(sql, [deviceId]);
    return result.affectedRows > 0;
  }

  // 验证设备ID格式（只能包含字母和数字，不限制位数）
  static validateDeviceId(deviceId) {
    const regex = /^[a-zA-Z0-9]+$/;
    return regex.test(deviceId);
  }
}

module.exports = Device;
