const { pool } = require('../config/database');

class Statistics {
  /**
   * 获取设备数量统计
   * @returns {Promise<Object>} 设备数量统计信息
   */
  static async getDeviceCount() {
    try {
      const [rows] = await pool.execute(`
        SELECT COUNT(*) as total_devices
        FROM Device
      `);

      return {
        total_devices: rows[0].total_devices
      };
    } catch (error) {
      console.error('获取设备数量统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储位置容量统计
   * @returns {Promise<Object>} 存储位置容量统计信息
   */
  static async getStorageCapacityStats() {
    try {
      // 获取存储位置总数和容量信息
      const [locationRows] = await pool.execute(`
        SELECT
          COUNT(*) as total_locations,
          COALESCE(SUM(capacity), 0) as total_capacity
        FROM StorageLocation
      `);

      // 获取每个存储位置的使用情况
      const [usageRows] = await pool.execute(`
        SELECT
          sl.location_id,
          sl.capacity,
          COUNT(d.device_id) as current_usage,
          (sl.capacity - COUNT(d.device_id)) as available_capacity,
          CASE
            WHEN sl.capacity = 0 THEN 0
            ELSE ROUND((COUNT(d.device_id) / sl.capacity) * 100, 2)
          END as utilization_rate
        FROM StorageLocation sl
        LEFT JOIN Device d ON sl.location_id = d.storage_location
        GROUP BY sl.location_id, sl.capacity
      `);

      // 计算总体使用情况
      const totalUsage = usageRows.reduce((sum, row) => sum + row.current_usage, 0);
      const totalCapacity = locationRows[0].total_capacity;
      const totalAvailable = totalCapacity - totalUsage;

      // 统计不同状态的存储位置数量
      const fullLocations = usageRows.filter(row => row.current_usage >= row.capacity).length;
      const emptyLocations = usageRows.filter(row => row.current_usage === 0).length;
      const partiallyUsedLocations = usageRows.filter(row => 
        row.current_usage > 0 && row.current_usage < row.capacity
      ).length;

      return {
        total_locations: locationRows[0].total_locations,
        total_capacity: totalCapacity,
        total_usage: totalUsage,
        total_available: totalAvailable,
        overall_utilization_rate: totalCapacity > 0 ? 
          Math.round((totalUsage / totalCapacity) * 100 * 100) / 100 : 0,
        location_status: {
          full_locations: fullLocations,           // 已装满的位置数
          empty_locations: emptyLocations,         // 空的位置数
          partially_used_locations: partiallyUsedLocations  // 部分使用的位置数
        },
        locations_detail: usageRows.map(row => ({
          location_id: row.location_id,
          capacity: row.capacity,
          current_usage: row.current_usage,
          available_capacity: row.available_capacity,
          utilization_rate: row.utilization_rate,
          status: row.current_usage === 0 ? 'empty' : 
                 row.current_usage >= row.capacity ? 'full' : 'partial'
        }))
      };
    } catch (error) {
      console.error('获取存储位置容量统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取综合统计信息
   * @returns {Promise<Object>} 综合统计信息
   */
  static async getOverviewStats() {
    try {
      const deviceStats = await this.getDeviceCount();
      const storageStats = await this.getStorageCapacityStats();

      return {
        devices: deviceStats,
        storage: storageStats,
        summary: {
          total_devices: deviceStats.total_devices,
          total_locations: storageStats.total_locations,
          total_capacity: storageStats.total_capacity,
          capacity_utilization: storageStats.overall_utilization_rate,
          available_space: storageStats.total_available
        }
      };
    } catch (error) {
      console.error('获取综合统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有模块的总数统计
   * @returns {Promise<Object>} 总数统计信息
   */
  static async getTotalCounts() {
    try {
      // 并行查询所有表的总数
      const [
        deviceResult,
        deviceTypeResult,
        storageLocationResult,
        outboundRecordResult,
        userResult
      ] = await Promise.all([
        pool.execute('SELECT COUNT(*) as count FROM Device'),
        pool.execute('SELECT COUNT(*) as count FROM DeviceType'),
        pool.execute('SELECT COUNT(*) as count FROM StorageLocation'),
        pool.execute('SELECT COUNT(*) as count FROM OutboundRecord'),
        pool.execute('SELECT COUNT(*) as count FROM User')
      ]);

      return {
        devices: deviceResult[0][0].count,
        device_types: deviceTypeResult[0][0].count,
        storage_locations: storageLocationResult[0][0].count,
        outbound_records: outboundRecordResult[0][0].count,
        users: userResult[0][0].count,
        total: deviceResult[0][0].count +
               deviceTypeResult[0][0].count +
               storageLocationResult[0][0].count +
               outboundRecordResult[0][0].count +
               userResult[0][0].count
      };
    } catch (error) {
      console.error('获取总数统计失败:', error);
      throw error;
    }
  }
}

module.exports = Statistics;
