const { query } = require('../config/database');

class StorageLocation {
  // 获取存储位置列表（支持分页和容量过滤）
  static async getList(page = 1, limit = 10, minCapacity = null, maxCapacity = null) {
    const offset = (page - 1) * limit;
    let sql = `
      SELECT 
        sl.*,
        COALESCE(device_count.count, 0) as current_usage,
        (sl.capacity - COALESCE(device_count.count, 0)) as available_capacity
      FROM StorageLocation sl
      LEFT JOIN (
        SELECT storage_location, COUNT(*) as count 
        FROM Device 
        GROUP BY storage_location
      ) device_count ON sl.location_id = device_count.storage_location
    `;
    
    let countSql = 'SELECT COUNT(*) as total FROM StorageLocation';
    const params = [];
    const countParams = [];
    const conditions = [];

    if (minCapacity !== null) {
      conditions.push('sl.capacity >= ?');
      params.push(minCapacity);
      countParams.push(minCapacity);
    }

    if (maxCapacity !== null) {
      conditions.push('sl.capacity <= ?');
      params.push(maxCapacity);
      countParams.push(maxCapacity);
    }

    if (conditions.length > 0) {
      const whereClause = ' WHERE ' + conditions.join(' AND ');
      sql += whereClause;
      countSql += whereClause.replace('sl.', '');
    }

    sql += ` ORDER BY sl.created_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const [items, totalResult] = await Promise.all([
      query(sql, params),
      query(countSql, countParams)
    ]);

    const total = totalResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      items,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: total,
        items_per_page: limit
      }
    };
  }

  // 根据ID获取存储位置详情
  static async findById(locationId) {
    const sql = `
      SELECT 
        sl.*,
        COALESCE(device_count.count, 0) as current_usage,
        (sl.capacity - COALESCE(device_count.count, 0)) as available_capacity
      FROM StorageLocation sl
      LEFT JOIN (
        SELECT storage_location, COUNT(*) as count 
        FROM Device 
        GROUP BY storage_location
      ) device_count ON sl.location_id = device_count.storage_location
      WHERE sl.location_id = ?
    `;
    const result = await query(sql, [locationId]);
    return result[0] || null;
  }

  // 获取存储位置的关联设备
  static async getDevices(locationId) {
    const sql = `
      SELECT 
        d.device_id,
        d.description,
        dt.type_name as device_type_name
      FROM Device d
      JOIN DeviceType dt ON d.device_type = dt.type_id
      WHERE d.storage_location = ?
      ORDER BY d.created_at DESC
    `;
    return await query(sql, [locationId]);
  }

  // 创建存储位置
  static async create(capacity, locationId = null) {
    if (capacity < 0) {
      throw new Error('容量不能为负数');
    }

    let sql, params;

    if (locationId) {
      // 检查自定义ID是否已存在
      const existing = await this.findById(locationId);
      if (existing) {
        throw new Error('存储位置ID已存在');
      }
      sql = 'INSERT INTO StorageLocation (location_id, capacity) VALUES (?, ?)';
      params = [locationId, capacity];
    } else {
      sql = 'INSERT INTO StorageLocation (capacity) VALUES (?)';
      params = [capacity];
    }

    const result = await query(sql, params);
    const finalId = locationId || result.insertId;
    return await this.findById(finalId);
  }

  // 更新存储位置
  static async update(locationId, capacity) {
    if (capacity < 0) {
      throw new Error('容量不能为负数');
    }

    // 检查新容量是否小于当前使用量
    const location = await this.findById(locationId);
    if (!location) {
      throw new Error('存储位置不存在');
    }

    if (capacity < location.current_usage) {
      throw new Error('新容量不能小于当前使用量');
    }

    const sql = 'UPDATE StorageLocation SET capacity = ?, updated_at = CURRENT_TIMESTAMP WHERE location_id = ?';
    await query(sql, [capacity, locationId]);
    return await this.findById(locationId);
  }

  // 删除存储位置
  static async delete(locationId) {
    // 检查是否有关联的设备
    const checkSql = 'SELECT COUNT(*) as count FROM Device WHERE storage_location = ?';
    const checkResult = await query(checkSql, [locationId]);
    
    if (checkResult[0].count > 0) {
      throw new Error('存在关联设备，无法删除该存储位置');
    }

    const sql = 'DELETE FROM StorageLocation WHERE location_id = ?';
    const result = await query(sql, [locationId]);
    return result.affectedRows > 0;
  }

  // 检查容量是否足够
  static async checkCapacity(locationId, additionalDevices = 1) {
    const location = await this.findById(locationId);
    if (!location) {
      return false;
    }
    return location.available_capacity >= additionalDevices;
  }

  // 获取所有存储位置（用于下拉选择）
  static async getAll() {
    const sql = `
      SELECT 
        sl.location_id,
        sl.capacity,
        COALESCE(device_count.count, 0) as current_usage,
        (sl.capacity - COALESCE(device_count.count, 0)) as available_capacity
      FROM StorageLocation sl
      LEFT JOIN (
        SELECT storage_location, COUNT(*) as count 
        FROM Device 
        GROUP BY storage_location
      ) device_count ON sl.location_id = device_count.storage_location
      ORDER BY sl.location_id
    `;
    return await query(sql);
  }
}

module.exports = StorageLocation;
