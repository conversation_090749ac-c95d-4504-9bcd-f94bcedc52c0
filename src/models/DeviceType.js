const { query } = require('../config/database');

class DeviceType {
  // 获取设备类型列表（支持分页和过滤）
  static async getList(page = 1, limit = 10, typeName = null) {
    const offset = (page - 1) * limit;
    let sql = 'SELECT * FROM DeviceType';
    let countSql = 'SELECT COUNT(*) as total FROM DeviceType';
    const params = [];
    const countParams = [];

    if (typeName) {
      sql += ' WHERE type_name LIKE ?';
      countSql += ' WHERE type_name LIKE ?';
      const searchTerm = `%${typeName}%`;
      params.push(searchTerm);
      countParams.push(searchTerm);
    }

    sql += ` ORDER BY created_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const [items, totalResult] = await Promise.all([
      query(sql, params),
      query(countSql, countParams)
    ]);

    const total = totalResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      items,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: total,
        items_per_page: parseInt(limit)
      }
    };
  }

  // 根据ID获取设备类型
  static async findById(typeId) {
    const sql = 'SELECT * FROM DeviceType WHERE type_id = ?';
    const result = await query(sql, [typeId]);
    return result[0] || null;
  }

  // 创建设备类型
  static async create(typeName, typeId = null) {
    let sql, params;

    if (typeId) {
      // 检查自定义ID是否已存在
      const existing = await this.findById(typeId);
      if (existing) {
        throw new Error('设备类型ID已存在');
      }
      sql = 'INSERT INTO DeviceType (type_id, type_name) VALUES (?, ?)';
      params = [typeId, typeName];
    } else {
      sql = 'INSERT INTO DeviceType (type_name) VALUES (?)';
      params = [typeName];
    }

    const result = await query(sql, params);
    const finalId = typeId || result.insertId;
    return await this.findById(finalId);
  }

  // 更新设备类型
  static async update(typeId, typeName) {
    const sql = 'UPDATE DeviceType SET type_name = ?, updated_at = CURRENT_TIMESTAMP WHERE type_id = ?';
    await query(sql, [typeName, typeId]);
    return await this.findById(typeId);
  }

  // 删除设备类型
  static async delete(typeId) {
    // 检查是否有关联的设备
    const checkSql = 'SELECT COUNT(*) as count FROM Device WHERE device_type = ?';
    const checkResult = await query(checkSql, [typeId]);
    
    if (checkResult[0].count > 0) {
      throw new Error('存在关联设备，无法删除该设备类型');
    }

    const sql = 'DELETE FROM DeviceType WHERE type_id = ?';
    const result = await query(sql, [typeId]);
    return result.affectedRows > 0;
  }

  // 获取所有设备类型（用于下拉选择）
  static async getAll() {
    const sql = 'SELECT type_id, type_name FROM DeviceType ORDER BY type_name';
    return await query(sql);
  }
}

module.exports = DeviceType;
