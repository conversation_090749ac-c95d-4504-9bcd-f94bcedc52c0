const { query } = require('../config/database');

class User {
  // 获取领用人列表
  static async getList(page = 1, limit = 10, userName = null) {
    const offset = (page - 1) * limit;
    let sql = 'SELECT * FROM User';
    let countSql = 'SELECT COUNT(*) as total FROM User';
    const params = [];
    const countParams = [];

    if (userName) {
      sql += ' WHERE name LIKE ?';
      countSql += ' WHERE name LIKE ?';
      const searchTerm = `%${userName}%`;
      params.push(searchTerm);
      countParams.push(searchTerm);
    }

    sql += ` ORDER BY created_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const [items, totalResult] = await Promise.all([
      query(sql, params),
      query(countSql, countParams)
    ]);

    return {
      items,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalResult[0].total,
        pages: Math.ceil(totalResult[0].total / limit)
      }
    };
  }

  // 根据ID获取领用人
  static async findById(userId) {
    const sql = 'SELECT * FROM User WHERE user_id = ?';
    const result = await query(sql, [userId]);
    return result[0] || null;
  }

  // 创建领用人
  static async create(name, department = null, contact = null) {
    // 检查用户名是否已存在
    const existing = await query('SELECT user_id FROM User WHERE name = ?', [name]);
    if (existing.length > 0) {
      throw new Error('领用人姓名已存在');
    }

    const sql = 'INSERT INTO User (name, department, contact) VALUES (?, ?, ?)';
    const result = await query(sql, [name, department, contact]);
    return await this.findById(result.insertId);
  }

  // 更新领用人
  static async update(userId, name, department = null, contact = null) {
    // 检查用户名是否已被其他人使用
    const existing = await query('SELECT user_id FROM User WHERE name = ? AND user_id != ?', [name, userId]);
    if (existing.length > 0) {
      throw new Error('领用人姓名已存在');
    }

    const sql = 'UPDATE User SET name = ?, department = ?, contact = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?';
    await query(sql, [name, department, contact, userId]);
    return await this.findById(userId);
  }

  // 删除领用人
  static async delete(userId) {
    // 检查是否有关联的出库记录
    const checkSql = 'SELECT COUNT(*) as count FROM OutboundRecord WHERE user_id = ?';
    const checkResult = await query(checkSql, [userId]);
    
    if (checkResult[0].count > 0) {
      throw new Error('存在关联出库记录，无法删除该领用人');
    }

    const sql = 'DELETE FROM User WHERE user_id = ?';
    const result = await query(sql, [userId]);
    return result.affectedRows > 0;
  }

  // 获取所有领用人（用于下拉选择）
  static async getAll() {
    const sql = 'SELECT user_id, user_name FROM User ORDER BY user_name';
    return await query(sql);
  }
}

module.exports = User;
